import { type Scraper, type InsertScraper, type UpdateScraper, type PriceHistory } from "@shared/schema";
import { randomUUID } from "crypto";
import { JSONFilePreset } from "lowdb/node";
import { calculatePriceStatistics } from "./src/utils/price-calculator";

export interface IStorage {
  // Scraper operations
  getScraper(id: string): Promise<Scraper | undefined>;
  getAllScrapers(): Promise<Scraper[]>;
  createScraper(scraper: InsertScraper): Promise<Scraper>;
  updateScraper(id: string, updates: UpdateScraper): Promise<Scraper | undefined>;
  deleteScraper(id: string): Promise<boolean>;

  // Price history operations
  addPriceHistory(scraperId: string, price: string): Promise<void>;
  getPriceHistory(scraperId: string): Promise<PriceHistory[]>;
  updateAveragePrice(scraperId: string): Promise<void>;
}

interface DatabaseSchema {
  scrapers: Scraper[];
  priceHistory: PriceHistory[];
}

export class JsonStorage implements IStorage {
  private db: Awaited<ReturnType<typeof JSONFilePreset<DatabaseSchema>>> | null = null;

  constructor() {
    this.init();
  }

  private async init() {
    this.db = await JSONFilePreset<DatabaseSchema>('db.json', { 
      scrapers: [], 
      priceHistory: [] 
    });
  }

  private async ensureDb() {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  async getScraper(id: string): Promise<Scraper | undefined> {
    const db = await this.ensureDb();
    const scraper = db.data.scrapers.find(scraper => scraper.id === id);
    if (!scraper) return undefined;

    const history = db.data.priceHistory.filter(entry => entry.scraperId === scraper.id);
    const priceStats = calculatePriceStatistics(history);

    // Use stored average price if available, otherwise use calculated one
    const averagePrice = scraper.averagePrice !== null ? scraper.averagePrice : priceStats.averagePrice;

    return {
      ...scraper,
      previousPrice: priceStats.previousPrice,
      averagePrice: averagePrice,
    };
  }

  async getAllScrapers(): Promise<Scraper[]> {
    const db = await this.ensureDb();
    return db.data.scrapers.map(scraper => {
      const history = db.data.priceHistory.filter(entry => entry.scraperId === scraper.id);
      const priceStats = calculatePriceStatistics(history);

      // Use stored average price if available, otherwise use calculated one
      const averagePrice = scraper.averagePrice !== null ? scraper.averagePrice : priceStats.averagePrice;

      return {
        ...scraper,
        previousPrice: priceStats.previousPrice,
        averagePrice: averagePrice,
      };
    });
  }

  async createScraper(insertScraper: InsertScraper): Promise<Scraper> {
    const db = await this.ensureDb();

    const id = randomUUID();
    const scraper: Scraper = {
      ...insertScraper,
      id,
      currentPrice: null,
      lowestPrice: null,
      averagePrice: null,
      status: "active",
      lastUpdated: new Date(),
      lastError: null,
      createdAt: new Date(),
    };

    await db.update(({ scrapers }) => scrapers.push(scraper));
    return scraper;
  }

  async updateScraper(id: string, updates: UpdateScraper): Promise<Scraper | undefined> {
    const db = await this.ensureDb();
    
    const scraperIndex = db.data.scrapers.findIndex(scraper => scraper.id === id);
    if (scraperIndex === -1) return undefined;

    const updatedScraper: Scraper = {
      ...db.data.scrapers[scraperIndex],
      ...updates,
      lastUpdated: new Date(),
    };
    
    await db.update(({ scrapers }) => {
      scrapers[scraperIndex] = updatedScraper;
    });
    
    return updatedScraper;
  }

  async deleteScraper(id: string): Promise<boolean> {
    const db = await this.ensureDb();
    
    const scraperIndex = db.data.scrapers.findIndex(scraper => scraper.id === id);
    if (scraperIndex === -1) return false;

    await db.update(({ scrapers, priceHistory }) => {
      scrapers.splice(scraperIndex, 1);
      // Remove associated price history
      const historyIndicesToRemove = [];
      for (let i = priceHistory.length - 1; i >= 0; i--) {
        if (priceHistory[i].scraperId === id) {
          historyIndicesToRemove.push(i);
        }
      }
      for (const index of historyIndicesToRemove) {
        priceHistory.splice(index, 1);
      }
    });
    
    return true;
  }

  async addPriceHistory(scraperId: string, price: string): Promise<void> {
    const db = await this.ensureDb();

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const existingEntryIndex = db.data.priceHistory.findIndex(entry => {
      const entryDate = new Date(entry.timestamp);
      entryDate.setHours(0, 0, 0, 0);
      return entry.scraperId === scraperId && entryDate.getTime() === today.getTime();
    });

    let priceUpdated = false;

    if (existingEntryIndex !== -1) {
      // Entry for today exists, check if the new price is lower
      const existingPrice = parseFloat(db.data.priceHistory[existingEntryIndex].price);
      const newPrice = parseFloat(price);

      if (newPrice < existingPrice) {
        // New price is lower, update the existing entry
        await db.update(({ priceHistory }) => {
          priceHistory[existingEntryIndex].price = price;
          priceHistory[existingEntryIndex].timestamp = new Date(); // Also update timestamp
        });
        priceUpdated = true;
      }
      // If new price is not lower, do nothing
    } else {
      // No entry for today, create a new one
      const newEntry: PriceHistory = {
        id: randomUUID(),
        scraperId,
        price,
        timestamp: new Date(),
      };
      await db.update(({ priceHistory }) => priceHistory.push(newEntry));
      priceUpdated = true;
    }

    // Update average price if price history was modified
    if (priceUpdated) {
      await this.updateAveragePrice(scraperId);
    }
  }

  async getPriceHistory(scraperId: string): Promise<PriceHistory[]> {
    const db = await this.ensureDb();
    return db.data.priceHistory.filter(entry => entry.scraperId === scraperId);
  }

  async updateAveragePrice(scraperId: string): Promise<void> {
    const db = await this.ensureDb();

    // Get price history for this scraper
    const history = db.data.priceHistory.filter(entry => entry.scraperId === scraperId);

    // Calculate new average price using the 90-day calculation
    const averagePrice = calculatePriceStatistics(history).averagePrice;

    // Update the scraper with the new average price in the database
    const scraperIndex = db.data.scrapers.findIndex(scraper => scraper.id === scraperId);
    if (scraperIndex !== -1) {
      await db.update(({ scrapers }) => {
        scrapers[scraperIndex].averagePrice = averagePrice;
      });
      console.log(`[STORAGE] Updated average price for scraper ${scraperId}: ${averagePrice}`);
    }
  }
}

export const storage = new JsonStorage();
