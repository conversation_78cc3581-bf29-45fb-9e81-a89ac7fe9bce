import type { PriceHistory } from "@shared/schema";

/**
 * Calculate the average price from the last 90 days of price history
 * @param priceHistory Array of price history entries for a scraper
 * @param scraperId Optional scraper ID for logging purposes
 * @returns Average price as string or null if no valid prices found
 */
export function calculate90DayAverage(priceHistory: PriceHistory[], scraperId?: string): string | null {
  const logPrefix = scraperId ? `[AVERAGE-CALC] Scraper ${scraperId}:` : '[AVERAGE-CALC]';

  if (!priceHistory || priceHistory.length === 0) {
    console.log(`${logPrefix} No price history available`);
    return null;
  }

  console.log(`${logPrefix} Calculating average from ${priceHistory.length} price history entries`);

  // Calculate the date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  // Filter price history to only include entries from the last 90 days
  // and only include entries with valid prices
  const validRecentHistory = priceHistory.filter(entry => {
    if (!entry || !entry.timestamp || !entry.price) {
      console.log(`${logPrefix} Skipping invalid entry:`, entry);
      return false;
    }

    const entryDate = new Date(entry.timestamp);
    if (isNaN(entryDate.getTime())) {
      console.log(`${logPrefix} Skipping entry with invalid timestamp:`, entry.timestamp);
      return false;
    }

    const price = parseFloat(entry.price);
    if (isNaN(price) || price <= 0) {
      console.log(`${logPrefix} Skipping entry with invalid price:`, entry.price);
      return false;
    }

    const isRecent = entryDate >= ninetyDaysAgo;
    const daysAgo = Math.floor((Date.now() - entryDate.getTime()) / (24 * 60 * 60 * 1000));
    console.log(`${logPrefix} Price ${entry.price} from ${daysAgo} days ago - ${isRecent ? 'included' : 'excluded (>90 days)'}`);

    return isRecent;
  });

  if (validRecentHistory.length === 0) {
    console.log(`${logPrefix} No valid recent price history found (within 90 days)`);
    return null;
  }

  console.log(`${logPrefix} Using ${validRecentHistory.length} valid entries for calculation`);

  // Calculate the average price
  let totalPrice = 0;

  for (const entry of validRecentHistory) {
    const price = parseFloat(entry.price);
    totalPrice += price;
    console.log(`${logPrefix} Adding price ${price} to total (running total: ${totalPrice.toFixed(2)})`);
  }

  const averagePrice = totalPrice / validRecentHistory.length;
  const result = averagePrice.toFixed(2);

  console.log(`${logPrefix} Final calculation: ${totalPrice.toFixed(2)} / ${validRecentHistory.length} = ${result}`);

  // Return the average price rounded to 2 decimal places
  return result;
}

/**
 * Calculate various price statistics for a scraper
 * @param priceHistory Array of price history entries for a scraper
 * @param scraperId Optional scraper ID for logging purposes
 * @returns Object containing current, lowest, previous, and average prices
 */
export function calculatePriceStatistics(priceHistory: PriceHistory[], scraperId?: string) {
  if (!priceHistory || priceHistory.length === 0) {
    return {
      currentPrice: null,
      lowestPrice: null,
      previousPrice: null,
      averagePrice: null,
    };
  }

  // Sort by timestamp descending (newest first)
  const sortedHistory = [...priceHistory].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  // Get current price (most recent entry)
  const currentPrice = sortedHistory[0]?.price || null;

  // Calculate lowest price from all history
  let lowestPrice: string | null = null;
  for (const entry of priceHistory) {
    const price = parseFloat(entry.price);
    if (!isNaN(price) && price > 0) {
      if (lowestPrice === null || price < parseFloat(lowestPrice)) {
        lowestPrice = entry.price;
      }
    }
  }

  // Get previous price (most recent entry from a different day than today)
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const previousHistory = sortedHistory.find(entry => {
    const entryDate = new Date(entry.timestamp);
    entryDate.setHours(0, 0, 0, 0);
    return entryDate.getTime() < today.getTime();
  });
  const previousPrice = previousHistory?.price || null;

  // Calculate 90-day average
  const averagePrice = calculate90DayAverage(priceHistory, scraperId);

  return {
    currentPrice,
    lowestPrice,
    previousPrice,
    averagePrice,
  };
}
