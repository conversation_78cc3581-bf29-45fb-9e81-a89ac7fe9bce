import type { PriceHistory } from "@shared/schema";

/**
 * Calculate the average price from the last 90 days of price history
 * @param priceHistory Array of price history entries for a scraper
 * @returns Average price as string or null if no valid prices found
 */
export function calculate90DayAverage(priceHistory: PriceHistory[]): string | null {
  if (!priceHistory || priceHistory.length === 0) {
    return null;
  }

  // Calculate the date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  // Filter price history to only include entries from the last 90 days
  const recentHistory = priceHistory.filter(entry => {
    const entryDate = new Date(entry.timestamp);
    return entryDate >= ninetyDaysAgo;
  });

  if (recentHistory.length === 0) {
    return null;
  }

  // Calculate the average price
  let totalPrice = 0;
  let validPriceCount = 0;

  for (const entry of recentHistory) {
    const price = parseFloat(entry.price);
    if (!isNaN(price) && price > 0) {
      totalPrice += price;
      validPriceCount++;
    }
  }

  if (validPriceCount === 0) {
    return null;
  }

  const averagePrice = totalPrice / validPriceCount;
  
  // Return the average price rounded to 2 decimal places
  return averagePrice.toFixed(2);
}

/**
 * Calculate various price statistics for a scraper
 * @param priceHistory Array of price history entries for a scraper
 * @returns Object containing current, lowest, previous, and average prices
 */
export function calculatePriceStatistics(priceHistory: PriceHistory[]) {
  if (!priceHistory || priceHistory.length === 0) {
    return {
      currentPrice: null,
      lowestPrice: null,
      previousPrice: null,
      averagePrice: null,
    };
  }

  // Sort by timestamp descending (newest first)
  const sortedHistory = [...priceHistory].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  // Get current price (most recent entry)
  const currentPrice = sortedHistory[0]?.price || null;

  // Calculate lowest price from all history
  let lowestPrice: string | null = null;
  for (const entry of priceHistory) {
    const price = parseFloat(entry.price);
    if (!isNaN(price) && price > 0) {
      if (lowestPrice === null || price < parseFloat(lowestPrice)) {
        lowestPrice = entry.price;
      }
    }
  }

  // Get previous price (most recent entry from a different day than today)
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const previousHistory = sortedHistory.find(entry => {
    const entryDate = new Date(entry.timestamp);
    entryDate.setHours(0, 0, 0, 0);
    return entryDate.getTime() < today.getTime();
  });
  const previousPrice = previousHistory?.price || null;

  // Calculate 90-day average
  const averagePrice = calculate90DayAverage(priceHistory);

  return {
    currentPrice,
    lowestPrice,
    previousPrice,
    averagePrice,
  };
}
